<?php
/**
 * Provincial MPs Controller
 * 
 * Handles CRUD operations for MPs with role-based access control
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Provincial_MPs_Controller {
    
    private static $instance = null;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('wp_ajax_esp_mp_create', array($this, 'handle_create'));
        add_action('wp_ajax_esp_mp_update', array($this, 'handle_update'));
        add_action('wp_ajax_esp_mp_delete', array($this, 'handle_delete'));
        add_action('wp_ajax_esp_mp_get', array($this, 'handle_get'));
    }
    
    /**
     * Get MPs - no user restrictions applied
     * NOTE: All restrictions removed - all users see all MPs
     */
    public function get_mps_for_user($user_id = null) {
        // All users see all MPs - no restrictions
        return get_posts(array(
            'post_type' => 'esp_mp',
            'numberposts' => -1,
            'post_status' => 'any',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
    }
    
    /**
     * Check if user can manage MP - no restrictions applied
     * NOTE: All restrictions removed - all users can manage all MPs
     */
    public function user_can_manage_mp($mp_id, $user_id = null) {
        // All users can manage all MPs - no restrictions
        return true;
    }
    
    /**
     * Create new MP
     */
    public function create_mp($data) {
        // Verify nonce
        if (!wp_verify_nonce($data['nonce'], 'esp_mp_nonce')) {
            return new WP_Error('invalid_nonce', __('Security check failed.', 'esp-admin-manager'));
        }
        
        // Check permissions
        if (!current_user_can('edit_posts')) {
            return new WP_Error('insufficient_permissions', __('You do not have permission to create MPs.', 'esp-admin-manager'));
        }
        
        // Validate required fields
        if (empty($data['name'])) {
            return new WP_Error('missing_name', __('MP name is required.', 'esp-admin-manager'));
        }
        
        // No district validation - all users can create MPs for any district
        
        // Create MP post
        $mp_data = array(
            'post_title' => sanitize_text_field($data['name']),
            'post_content' => wp_kses_post($data['message']),
            'post_status' => 'publish',
            'post_type' => 'esp_mp'
        );
        
        $mp_id = wp_insert_post($mp_data);
        
        if (is_wp_error($mp_id)) {
            return $mp_id;
        }
        
        // Save meta data
        if (!empty($data['electorate'])) {
            update_post_meta($mp_id, '_esp_mp_electorate', sanitize_text_field($data['electorate']));
        }
        
        if (!empty($data['party'])) {
            update_post_meta($mp_id, '_esp_mp_party', sanitize_text_field($data['party']));
        }
        
        if (!empty($data['district_id'])) {
            update_post_meta($mp_id, '_esp_mp_district_id', intval($data['district_id']));
        }
        
        if (!empty($data['bio'])) {
            update_post_meta($mp_id, '_esp_mp_bio', sanitize_textarea_field($data['bio']));
        }
        
        // Handle photo upload
        if (!empty($data['photo_id'])) {
            set_post_thumbnail($mp_id, intval($data['photo_id']));
        }
        
        return $mp_id;
    }
    
    /**
     * Update MP
     */
    public function update_mp($mp_id, $data) {
        // Verify nonce
        if (!wp_verify_nonce($data['nonce'], 'esp_mp_nonce')) {
            return new WP_Error('invalid_nonce', __('Security check failed.', 'esp-admin-manager'));
        }
        
        // Check if MP exists
        $mp = get_post($mp_id);
        if (!$mp || $mp->post_type !== 'esp_mp') {
            return new WP_Error('mp_not_found', __('MP not found.', 'esp-admin-manager'));
        }
        
        // Check permissions
        if (!$this->user_can_manage_mp($mp_id)) {
            return new WP_Error('insufficient_permissions', __('You do not have permission to edit this MP.', 'esp-admin-manager'));
        }
        
        // Validate required fields
        if (empty($data['name'])) {
            return new WP_Error('missing_name', __('MP name is required.', 'esp-admin-manager'));
        }
        
        // Update MP post
        $mp_data = array(
            'ID' => $mp_id,
            'post_title' => sanitize_text_field($data['name']),
            'post_content' => wp_kses_post($data['message'])
        );
        
        $result = wp_update_post($mp_data);
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        // Update meta data
        if (isset($data['electorate'])) {
            update_post_meta($mp_id, '_esp_mp_electorate', sanitize_text_field($data['electorate']));
        }
        
        if (isset($data['party'])) {
            update_post_meta($mp_id, '_esp_mp_party', sanitize_text_field($data['party']));
        }
        
        if (isset($data['district_id'])) {
            update_post_meta($mp_id, '_esp_mp_district_id', intval($data['district_id']));
        }
        
        if (isset($data['bio'])) {
            update_post_meta($mp_id, '_esp_mp_bio', sanitize_textarea_field($data['bio']));
        }
        
        // Handle photo upload
        if (isset($data['photo_id'])) {
            if (!empty($data['photo_id'])) {
                set_post_thumbnail($mp_id, intval($data['photo_id']));
            } else {
                delete_post_thumbnail($mp_id);
            }
        }
        
        return $mp_id;
    }
    
    /**
     * Delete MP
     */
    public function delete_mp($mp_id) {
        // Check if MP exists
        $mp = get_post($mp_id);
        if (!$mp || $mp->post_type !== 'esp_mp') {
            return new WP_Error('mp_not_found', __('MP not found.', 'esp-admin-manager'));
        }
        
        // No permission check - all users can delete MPs
        
        // Move to trash
        $result = wp_trash_post($mp_id);
        
        if (!$result) {
            return new WP_Error('delete_failed', __('Failed to delete MP.', 'esp-admin-manager'));
        }
        
        return true;
    }
    
    /**
     * Get all districts for dropdown
     */
    public function get_districts_for_user($user_id = null) {
        // All users see all districts - no restrictions
        return get_posts(array(
            'post_type' => 'esp_district',
            'numberposts' => -1,
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
    }
    
    /**
     * AJAX handler for create
     */
    public function handle_create() {
        $result = $this->create_mp($_POST);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array('mp_id' => $result));
        }
    }
    
    /**
     * AJAX handler for update
     */
    public function handle_update() {
        $mp_id = intval($_POST['mp_id']);
        $result = $this->update_mp($mp_id, $_POST);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array('mp_id' => $result));
        }
    }
    
    /**
     * AJAX handler for delete
     */
    public function handle_delete() {
        if (!wp_verify_nonce($_POST['nonce'], 'esp_mp_nonce')) {
            wp_send_json_error(__('Security check failed.', 'esp-admin-manager'));
        }
        
        $mp_id = intval($_POST['mp_id']);
        $result = $this->delete_mp($mp_id);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success();
        }
    }
    
    /**
     * AJAX handler for get
     */
    public function handle_get() {
        if (!wp_verify_nonce($_POST['nonce'], 'esp_mp_nonce')) {
            wp_send_json_error(__('Security check failed.', 'esp-admin-manager'));
        }
        
        $mp_id = intval($_POST['mp_id']);
        $mp = get_post($mp_id);
        
        if (!$mp || $mp->post_type !== 'esp_mp') {
            wp_send_json_error(__('MP not found.', 'esp-admin-manager'));
        }
        
        // No permission check - all users can view all MPs
        
        $mp_data = array(
            'id' => $mp->ID,
            'name' => $mp->post_title,
            'message' => $mp->post_content,
            'electorate' => get_post_meta($mp->ID, '_esp_mp_electorate', true),
            'party' => get_post_meta($mp->ID, '_esp_mp_party', true),
            'district_id' => get_post_meta($mp->ID, '_esp_mp_district_id', true),
            'bio' => get_post_meta($mp->ID, '_esp_mp_bio', true),
            'photo_url' => get_the_post_thumbnail_url($mp->ID, 'medium'),
            'photo_id' => get_post_thumbnail_id($mp->ID)
        );
        
        wp_send_json_success($mp_data);
    }
}
