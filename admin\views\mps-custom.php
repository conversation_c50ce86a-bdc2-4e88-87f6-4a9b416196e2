<?php
/**
 * Provincial Administration Manager - Custom MPs Management View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get MPs controller instance
$mps_controller = Provincial_MPs_Controller::get_instance();

// Get all MPs and districts - no user restrictions applied
$current_user_id = get_current_user_id();
$user_type = get_user_meta($current_user_id, 'provincial_user_type', true);
$is_district_user = false; // No longer restrict based on user type
$is_provincial_user = true; // All users have provincial access
$is_admin_user = true; // All users have admin access for this view

// All users see all MPs and districts - no restrictions
$mps = $mps_controller->get_mps_for_user($current_user_id);
$districts = $mps_controller->get_districts_for_user($current_user_id);

// Get district names for display (for district users)
$district_names = array();
if ($is_district_user && !$is_admin_user) {
    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
    foreach ($assigned_districts as $district_id) {
        $district = get_post($district_id);
        if ($district && $district->post_status === 'publish') {
            $district_names[] = $district->post_title;
        }
    }
}


?>

<div class="esp-custom-admin-wrap">
    <!-- Header Section -->
    <div class="esp-admin-header">
        <div class="esp-header-content">
            <h1 class="esp-page-title">
                <span class="esp-icon">👥</span>
                <?php if ($is_district_user && !$is_admin_user): ?>
                    <?php _e('My District MPs', 'esp-admin-manager'); ?>
                <?php else: ?>
                    <?php _e('Members of Parliament', 'esp-admin-manager'); ?>
                <?php endif; ?>
            </h1>
            <p class="esp-page-description">
                <?php if ($is_district_user && !$is_admin_user): ?>
                    <?php _e('Manage MPs for your assigned districts', 'esp-admin-manager'); ?>
                <?php else: ?>
                    <?php _e('Manage all Members of Parliament profiles and information', 'esp-admin-manager'); ?>
                <?php endif; ?>
            </p>
        </div>
        <?php if (!($is_district_user && !$is_admin_user)): ?>
        <div class="esp-header-actions">
            <button type="button" class="esp-btn esp-btn-primary" id="add-new-mp">
                <span class="esp-btn-icon">➕</span>
                <?php _e('Add New MP', 'esp-admin-manager'); ?>
            </button>
        </div>
        <?php endif; ?>
    </div>

    <!-- District Info for District Users -->
    <?php if ($is_district_user && !$is_admin_user && !empty($district_names)): ?>
        <div class="esp-info-banner">
            <div class="esp-info-content">
                <span class="esp-info-icon">ℹ️</span>
                <div class="esp-info-text">
                    <strong><?php _e('Managing MPs for:', 'esp-admin-manager'); ?></strong>
                    <?php echo esc_html(implode(', ', $district_names)); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- MPs Grid -->
    <div class="esp-content-section">
        <?php if (!empty($mps)): ?>
            <div class="esp-mps-grid">
                <?php foreach ($mps as $mp):
                    $electorate = get_post_meta($mp->ID, '_esp_mp_electorate', true);
                    $party = get_post_meta($mp->ID, '_esp_mp_party', true);
                    $district_id = get_post_meta($mp->ID, '_esp_mp_district_id', true);
                    $bio = get_post_meta($mp->ID, '_esp_mp_bio', true);
                    $photo_url = get_the_post_thumbnail_url($mp->ID, 'medium');
                    
                    // Get district name
                    $district_name = '';
                    if ($district_id) {
                        $district = get_post($district_id);
                        if ($district) {
                            $district_name = $district->post_title;
                        }
                    }
                ?>
                    <div class="esp-mp-card" data-mp-id="<?php echo esc_attr($mp->ID); ?>">
                        <div class="esp-mp-photo">
                            <?php if ($photo_url): ?>
                                <img src="<?php echo esc_url($photo_url); ?>" alt="<?php echo esc_attr($mp->post_title); ?>">
                            <?php else: ?>
                                <div class="esp-mp-placeholder">
                                    <span class="esp-placeholder-icon">👤</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="esp-mp-info">
                            <h3 class="esp-mp-name"><?php echo esc_html($mp->post_title); ?></h3>
                            
                            <?php if ($electorate): ?>
                                <div class="esp-mp-detail">
                                    <span class="esp-detail-label"><?php _e('Electorate:', 'esp-admin-manager'); ?></span>
                                    <span class="esp-detail-value"><?php echo esc_html($electorate); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($party): ?>
                                <div class="esp-mp-detail">
                                    <span class="esp-detail-label"><?php _e('Party:', 'esp-admin-manager'); ?></span>
                                    <span class="esp-detail-value"><?php echo esc_html($party); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($district_name): ?>
                                <div class="esp-mp-detail">
                                    <span class="esp-detail-label"><?php _e('District:', 'esp-admin-manager'); ?></span>
                                    <span class="esp-detail-value"><?php echo esc_html($district_name); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <div class="esp-mp-status">
                                <span class="esp-status esp-status-<?php echo esc_attr($mp->post_status); ?>">
                                    <?php echo esc_html(ucfirst($mp->post_status)); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="esp-mp-actions">
                            <button type="button" class="esp-btn esp-btn-secondary esp-btn-sm edit-mp" 
                                    data-mp-id="<?php echo esc_attr($mp->ID); ?>">
                                <span class="esp-btn-icon">✏️</span>
                                <?php _e('Edit', 'esp-admin-manager'); ?>
                            </button>
                            
                            <?php if ($is_admin_user || $is_provincial_user): ?>
                                <button type="button" class="esp-btn esp-btn-danger esp-btn-sm delete-mp" 
                                        data-mp-id="<?php echo esc_attr($mp->ID); ?>"
                                        data-mp-name="<?php echo esc_attr($mp->post_title); ?>">
                                    <span class="esp-btn-icon">🗑️</span>
                                    <?php _e('Delete', 'esp-admin-manager'); ?>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="esp-empty-state">
                <div class="esp-empty-icon">👥</div>
                <h3 class="esp-empty-title">
                    <?php if ($is_district_user && !$is_admin_user): ?>
                        <?php _e('No MPs found for your districts', 'esp-admin-manager'); ?>
                    <?php else: ?>
                        <?php _e('No MPs found', 'esp-admin-manager'); ?>
                    <?php endif; ?>
                </h3>
                <p class="esp-empty-description">
                    <?php if ($is_district_user && !$is_admin_user): ?>
                        <?php _e('No MPs have been assigned to your districts yet. Please contact your administrator to add MPs for your districts.', 'esp-admin-manager'); ?>
                    <?php else: ?>
                        <?php _e('Get started by adding your first MP using the "Add New MP" button above.', 'esp-admin-manager'); ?>
                    <?php endif; ?>
                </p>
                <?php if (!($is_district_user && !$is_admin_user)): ?>
                <button type="button" class="esp-btn esp-btn-primary" id="add-first-mp">
                    <span class="esp-btn-icon">➕</span>
                    <?php _e('Add Your First MP', 'esp-admin-manager'); ?>
                </button>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- MP Form Modal -->
<div id="mp-form-modal" class="esp-modal" style="display: none;">
    <div class="esp-modal-overlay"></div>
    <div class="esp-modal-content">
        <div class="esp-modal-header">
            <h2 id="modal-title"><?php _e('Add New MP', 'esp-admin-manager'); ?></h2>
            <button type="button" class="esp-modal-close" id="close-modal">×</button>
        </div>
        
        <form id="mp-form" class="esp-form">
            <input type="hidden" id="mp-id" name="mp_id" value="">
            <input type="hidden" name="action" value="esp_mp_create">
            <input type="hidden" name="nonce" value="<?php echo wp_create_nonce('esp_mp_nonce'); ?>">
            
            <div class="esp-form-row">
                <div class="esp-form-group">
                    <label for="mp-name" class="esp-label required"><?php _e('MP Name', 'esp-admin-manager'); ?></label>
                    <input type="text" id="mp-name" name="name" class="esp-input" required>
                </div>
                
                <div class="esp-form-group">
                    <label for="mp-electorate" class="esp-label"><?php _e('Electorate', 'esp-admin-manager'); ?></label>
                    <input type="text" id="mp-electorate" name="electorate" class="esp-input" 
                           placeholder="<?php _e('e.g., Wewak Open, Provincial', 'esp-admin-manager'); ?>">
                </div>
            </div>
            
            <div class="esp-form-row">
                <div class="esp-form-group">
                    <label for="mp-party" class="esp-label"><?php _e('Political Party', 'esp-admin-manager'); ?></label>
                    <input type="text" id="mp-party" name="party" class="esp-input" 
                           placeholder="<?php _e('e.g., Pangu Party, PNC', 'esp-admin-manager'); ?>">
                </div>
                
                <div class="esp-form-group">
                    <label for="mp-district" class="esp-label"><?php _e('District', 'esp-admin-manager'); ?></label>
                    <select id="mp-district" name="district_id" class="esp-select">
                        <option value=""><?php _e('Select District', 'esp-admin-manager'); ?></option>
                        <?php foreach ($districts as $district): ?>
                            <option value="<?php echo esc_attr($district->ID); ?>">
                                <?php echo esc_html($district->post_title); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <div class="esp-form-group">
                <label for="mp-message" class="esp-label"><?php _e('MP Message', 'esp-admin-manager'); ?></label>
                <textarea id="mp-message" name="message" class="esp-textarea" rows="4"
                          placeholder="<?php _e('Public message to constituents...', 'esp-admin-manager'); ?>"></textarea>
                <small class="esp-help-text"><?php _e('This message will be displayed publicly on the website.', 'esp-admin-manager'); ?></small>
            </div>
            
            <div class="esp-form-group">
                <label for="mp-bio" class="esp-label"><?php _e('MP Biography', 'esp-admin-manager'); ?></label>
                <textarea id="mp-bio" name="bio" class="esp-textarea" rows="3"
                          placeholder="<?php _e('Biographical information about the MP...', 'esp-admin-manager'); ?>"></textarea>
                <small class="esp-help-text"><?php _e('Background information, education, career history, etc.', 'esp-admin-manager'); ?></small>
            </div>
            
            <div class="esp-form-group">
                <label class="esp-label"><?php _e('MP Photo', 'esp-admin-manager'); ?></label>
                <div class="esp-media-upload">
                    <input type="hidden" id="mp-photo-id" name="photo_id" value="">
                    <div id="photo-preview" class="esp-photo-preview" style="display: none;">
                        <img id="photo-image" src="" alt="">
                        <button type="button" id="remove-photo" class="esp-btn esp-btn-danger esp-btn-sm">
                            <?php _e('Remove Photo', 'esp-admin-manager'); ?>
                        </button>
                    </div>
                    <button type="button" id="upload-photo" class="esp-btn esp-btn-secondary">
                        <span class="esp-btn-icon">📷</span>
                        <?php _e('Upload Photo', 'esp-admin-manager'); ?>
                    </button>
                </div>
            </div>
            
            <div class="esp-form-actions">
                <button type="button" class="esp-btn esp-btn-secondary" id="cancel-form">
                    <?php _e('Cancel', 'esp-admin-manager'); ?>
                </button>
                <button type="submit" class="esp-btn esp-btn-primary" id="save-mp">
                    <span class="esp-btn-icon">💾</span>
                    <span id="save-text"><?php _e('Save MP', 'esp-admin-manager'); ?></span>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Pass data to JavaScript
window.espMPsData = {
    ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
    nonce: '<?php echo wp_create_nonce('esp_mp_nonce'); ?>',
    isDistrictUser: <?php echo json_encode($is_district_user && !$is_admin_user); ?>,
    canDelete: <?php echo json_encode($is_admin_user || $is_provincial_user); ?>,
    strings: {
        confirmDelete: '<?php _e('Are you sure you want to delete this MP?', 'esp-admin-manager'); ?>',
        editMP: '<?php _e('Edit MP', 'esp-admin-manager'); ?>',
        addMP: '<?php _e('Add New MP', 'esp-admin-manager'); ?>',
        updateMP: '<?php _e('Update MP', 'esp-admin-manager'); ?>',
        saveMP: '<?php _e('Save MP', 'esp-admin-manager'); ?>',
        saving: '<?php _e('Saving...', 'esp-admin-manager'); ?>',
        updating: '<?php _e('Updating...', 'esp-admin-manager'); ?>',
        deleting: '<?php _e('Deleting...', 'esp-admin-manager'); ?>',
        success: '<?php _e('Success!', 'esp-admin-manager'); ?>',
        error: '<?php _e('Error occurred. Please try again.', 'esp-admin-manager'); ?>'
    }
};
</script>
