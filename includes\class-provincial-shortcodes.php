<?php
/**
 * Provincial Administration Shortcodes Class
 * 
 * Handles shortcodes for frontend display
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Provincial_Shortcodes {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->register_shortcodes();
    }
    
    /**
     * Check shortcode permissions
     */
    private function check_shortcode_permission($shortcode_type, $level = 'district', $district_id = null) {
        // If user is not logged in, allow public viewing
        if (!is_user_logged_in()) {
            return true;
        }

        $user_id = get_current_user_id();
        $user_type = Provincial_User_Roles::get_user_provincial_type($user_id);

        // Administrators can see everything
        if ($user_type === 'administrator' || current_user_can('manage_options')) {
            return true;
        }

        // No restrictions - all users can access all shortcodes
        return true;
    }

    /**
     * Filter content by user district access - no restrictions applied
     * NOTE: All restrictions removed - all users see all content
     */
    private function filter_content_by_district_access($content, $content_type = 'post') {
        // No filtering - all users see all content
        return $content;
    }

    /**
     * Register all shortcodes
     *
     * Shortcode Naming Convention:
     * - Primary: dakoii_prov_admin_* (aligns with plugin name "Dakoii Provincial Administration Manager")
     * - Alternative: dakoii_* (shorter, brand-focused)
     * - Generic: provincial_* (generic provincial administration)
     * - Legacy: esp_* (backward compatibility only)
     *
     * All shortcodes produce identical output for maximum flexibility.
     */
    public function register_shortcodes() {
        // Primary Dakoii Provincial Administration Manager shortcodes (recommended)
        add_shortcode('dakoii_prov_admin_governor', array($this, 'governor_shortcode'));
        add_shortcode('dakoii_prov_admin_mps', array($this, 'mps_shortcode'));
        add_shortcode('dakoii_prov_admin_mp', array($this, 'single_mp_shortcode'));
        add_shortcode('dakoii_prov_admin_mp_only', array($this, 'single_mp_shortcode'));
        add_shortcode('dakoii_prov_admin_mp_message', array($this, 'mp_message_shortcode'));
        add_shortcode('dakoii_prov_admin_districts', array($this, 'districts_shortcode'));
        add_shortcode('dakoii_prov_admin_statistics', array($this, 'statistics_shortcode'));
        add_shortcode('dakoii_prov_admin_structure', array($this, 'admin_structure_shortcode'));
        add_shortcode('dakoii_prov_admin_events', array($this, 'events_shortcode'));
        add_shortcode('dakoii_prov_admin_news', array($this, 'news_shortcode'));
        add_shortcode('dakoii_prov_admin_recent_news', array($this, 'recent_news_shortcode'));
        add_shortcode('dakoii_prov_admin_upcoming_events', array($this, 'upcoming_events_shortcode'));
        add_shortcode('dakoii_prov_admin_contact', array($this, 'contact_shortcode'));
        add_shortcode('dakoii_prov_admin_district_contact', array($this, 'district_contact_shortcode'));
        add_shortcode('dakoii_prov_admin_provincial_contact', array($this, 'provincial_contact_shortcode'));
        add_shortcode('dakoii_prov_admin_slideshow', array($this, 'slideshow_shortcode'));

        // Alternative shorter Dakoii shortcodes (also recommended)
        add_shortcode('dakoii_governor', array($this, 'governor_shortcode'));
        add_shortcode('dakoii_mps', array($this, 'mps_shortcode'));
        add_shortcode('dakoii_mp', array($this, 'single_mp_shortcode'));
        add_shortcode('dakoii_mp_only', array($this, 'single_mp_shortcode'));
        add_shortcode('dakoii_mp_message', array($this, 'mp_message_shortcode'));
        add_shortcode('dakoii_districts', array($this, 'districts_shortcode'));
        add_shortcode('dakoii_statistics', array($this, 'statistics_shortcode'));
        add_shortcode('dakoii_admin_structure', array($this, 'admin_structure_shortcode'));
        add_shortcode('dakoii_events', array($this, 'events_shortcode'));
        add_shortcode('dakoii_news', array($this, 'news_shortcode'));
        add_shortcode('dakoii_recent_news', array($this, 'recent_news_shortcode'));
        add_shortcode('dakoii_upcoming_events', array($this, 'upcoming_events_shortcode'));
        add_shortcode('dakoii_contact', array($this, 'contact_shortcode'));
        add_shortcode('dakoii_district_contact', array($this, 'district_contact_shortcode'));
        add_shortcode('dakoii_provincial_contact', array($this, 'provincial_contact_shortcode'));
        add_shortcode('dakoii_slideshow', array($this, 'slideshow_shortcode'));
        add_shortcode('dakoii_map', array($this, 'map_shortcode'));
        add_shortcode('dakoii_maps', array($this, 'maps_shortcode'));

        // Generic provincial shortcodes (for compatibility)
        add_shortcode('provincial_governor', array($this, 'governor_shortcode'));
        add_shortcode('provincial_mps', array($this, 'mps_shortcode'));
        add_shortcode('provincial_mp', array($this, 'single_mp_shortcode'));
        add_shortcode('provincial_mp_only', array($this, 'single_mp_shortcode'));
        add_shortcode('provincial_mp_message', array($this, 'mp_message_shortcode'));
        add_shortcode('provincial_districts', array($this, 'districts_shortcode'));
        add_shortcode('provincial_statistics', array($this, 'statistics_shortcode'));
        add_shortcode('provincial_admin_structure', array($this, 'admin_structure_shortcode'));
        add_shortcode('provincial_events', array($this, 'events_shortcode'));
        add_shortcode('provincial_news', array($this, 'news_shortcode'));
        add_shortcode('provincial_recent_news', array($this, 'recent_news_shortcode'));
        add_shortcode('provincial_upcoming_events', array($this, 'upcoming_events_shortcode'));
        add_shortcode('provincial_contact', array($this, 'contact_shortcode'));
        add_shortcode('provincial_district_contact', array($this, 'district_contact_shortcode'));
        add_shortcode('provincial_provincial_contact', array($this, 'provincial_contact_shortcode'));
        add_shortcode('provincial_slideshow', array($this, 'slideshow_shortcode'));

        // Legacy ESP shortcodes (backward compatibility only)
        add_shortcode('esp_governor', array($this, 'governor_shortcode'));
        add_shortcode('esp_mps', array($this, 'mps_shortcode'));
        add_shortcode('esp_mp', array($this, 'single_mp_shortcode'));
        add_shortcode('esp_mp_only', array($this, 'single_mp_shortcode'));
        add_shortcode('esp_mp_message', array($this, 'mp_message_shortcode'));
        add_shortcode('esp_districts', array($this, 'districts_shortcode'));
        add_shortcode('esp_statistics', array($this, 'statistics_shortcode'));
        add_shortcode('esp_admin_structure', array($this, 'admin_structure_shortcode'));
        add_shortcode('esp_events', array($this, 'events_shortcode'));
        add_shortcode('esp_news', array($this, 'news_shortcode'));
        add_shortcode('esp_recent_news', array($this, 'recent_news_shortcode'));
        add_shortcode('esp_upcoming_events', array($this, 'upcoming_events_shortcode'));
        add_shortcode('esp_contact', array($this, 'contact_shortcode'));
        add_shortcode('esp_district_contact', array($this, 'district_contact_shortcode'));
        add_shortcode('esp_provincial_contact', array($this, 'provincial_contact_shortcode'));
        add_shortcode('esp_slideshow', array($this, 'slideshow_shortcode'));
    }
    
    /**
     * Governor shortcode
     */
    public function governor_shortcode($atts) {
        $atts = shortcode_atts(array(
            'show_photo' => 'true',
            'show_message' => 'true',
            'level' => 'provincial'
        ), $atts);

        // Check permissions
        if (!$this->check_shortcode_permission('governor', $atts['level'])) {
            return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
        }

        $governors = get_posts(array(
            'post_type' => 'esp_governor',
            'numberposts' => 1,
            'post_status' => 'publish'
        ));

        if (empty($governors)) {
            return '<p>' . __('No governor profile found.', 'esp-admin-manager') . '</p>';
        }
        
        $governor = $governors[0];
        $title = get_post_meta($governor->ID, '_esp_governor_title', true);
        $party = get_post_meta($governor->ID, '_esp_governor_party', true);
        
        ob_start();
        ?>
        <section class="esp-governor-section">
            <div class="esp-governor-grid">
                <?php if ($atts['show_photo'] === 'true'): ?>
                <div class="esp-governor-photo">
                    <?php
                    $photo_id = get_post_thumbnail_id($governor->ID);
                    $photo_url = $photo_id ? wp_get_attachment_image_url($photo_id, 'medium') : '';
                    ?>
                    <?php if ($photo_url): ?>
                        <img src="<?php echo esc_url($photo_url); ?>" alt="<?php echo esc_attr($governor->post_title); ?>" />
                    <?php else: ?>
                        <div class="esp-placeholder-photo">👤</div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
                <div class="esp-governor-content">
                    <h2><?php echo esc_html($governor->post_title); ?></h2>
                    <?php if ($title): ?>
                        <div class="esp-governor-title"><?php echo esc_html($title); ?></div>
                    <?php endif; ?>
                    <?php if ($party): ?>
                        <div class="esp-governor-party"><?php echo esc_html($party); ?></div>
                    <?php endif; ?>
                    
                    <?php if ($atts['show_message'] === 'true' && $governor->post_content): ?>
                        <div class="esp-governor-message">
                            <?php echo wp_kses_post($governor->post_content); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
    
    /**
     * MPs shortcode
     */
    public function mps_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => -1,
            'show_photos' => 'true',
            'show_messages' => 'false',
            'show_districts' => 'false',
            'district_id' => '',
            'level' => 'district'
        ), $atts);

        // Check permissions
        $district_id = !empty($atts['district_id']) ? intval($atts['district_id']) : null;
        if (!$this->check_shortcode_permission('mps', $atts['level'], $district_id)) {
            return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
        }

        $query_args = array(
            'post_type' => 'esp_mp',
            'numberposts' => intval($atts['limit']),
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        );

        // Filter by district if specified
        if (!empty($atts['district_id'])) {
            $query_args['meta_query'] = array(
                array(
                    'key' => '_esp_mp_district_id',
                    'value' => intval($atts['district_id']),
                    'compare' => '='
                )
            );
        }

        $mps = get_posts($query_args);

        // Filter content by user access
        $mps = $this->filter_content_by_district_access($mps);

        if (empty($mps)) {
            return '<p>' . __('No MPs found.', 'esp-admin-manager') . '</p>';
        }
        
        ob_start();
        ?>
        <section class="esp-parliament-section">
            <div class="esp-mp-grid">
                <?php foreach ($mps as $mp):
                    $electorate = get_post_meta($mp->ID, '_esp_mp_electorate', true);
                    $party = get_post_meta($mp->ID, '_esp_mp_party', true);
                    $district_id = get_post_meta($mp->ID, '_esp_mp_district_id', true);
                    $district_name = '';
                    if ($district_id) {
                        $district = get_post($district_id);
                        if ($district && $district->post_status === 'publish') {
                            $district_name = $district->post_title;
                        }
                    }
                    $message = $mp->post_content; // Message is now from post content
                ?>
                    <div class="esp-mp-card">
                        <?php if ($atts['show_photos'] === 'true'): ?>
                            <div class="esp-mp-photo">
                                <?php if (has_post_thumbnail($mp->ID)): ?>
                                    <?php echo get_the_post_thumbnail($mp->ID, 'thumbnail'); ?>
                                <?php else: ?>
                                    👤
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <div class="esp-mp-name"><?php echo esc_html($mp->post_title); ?></div>

                        <?php if ($electorate): ?>
                            <div class="esp-mp-electorate"><?php echo esc_html($electorate); ?></div>
                        <?php endif; ?>

                        <?php if ($party): ?>
                            <div class="esp-mp-party"><?php echo esc_html($party); ?></div>
                    <?php endif; ?>

                    <?php if ($atts['show_districts'] === 'true' && $district_name): ?>
                        <div class="esp-mp-district"><?php echo esc_html($district_name); ?></div>
                        <?php endif; ?>

                        <?php if ($atts['show_messages'] === 'true' && $message): ?>
                            <div class="esp-mp-message"><?php echo wp_kses_post(wpautop($message)); ?></div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }

    /**
     * Single MP shortcode
     */
    public function single_mp_shortcode($atts, $content = null, $tag = '') {
        $atts = shortcode_atts(array(
            'id' => '',
            'show_photo' => 'true',
            'show_message' => 'true',
            'level' => 'district',
            'district_id' => ''
        ), $atts);

        // If called via *_mp_only shortcode, disable message display
        if (strpos($tag, '_mp_only') !== false) {
            $atts['show_message'] = 'false';
        }

        // Check if MP ID is provided
        if (empty($atts['id'])) {
            return '<p>' . __('MP ID is required. Usage: [dakoii_mp id="123"]', 'esp-admin-manager') . '</p>';
        }

        // Check permissions
        $district_id = !empty($atts['district_id']) ? intval($atts['district_id']) : null;
        if (!$this->check_shortcode_permission('mps', $atts['level'], $district_id)) {
            return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
        }

        // Get the specific MP
        $mp = get_post(intval($atts['id']));

        if (!$mp || $mp->post_type !== 'esp_mp' || $mp->post_status !== 'publish') {
            return '<p>' . __('MP not found or not published.', 'esp-admin-manager') . '</p>';
        }

        // Filter content by user access
        $mps_array = array($mp);
        $filtered_mps = $this->filter_content_by_district_access($mps_array);

        if (empty($filtered_mps)) {
            return '<p>' . __('You do not have permission to view this MP.', 'esp-admin-manager') . '</p>';
        }

        // Get MP data
        $electorate = get_post_meta($mp->ID, '_esp_mp_electorate', true);
        $party = get_post_meta($mp->ID, '_esp_mp_party', true);
        $district_id = get_post_meta($mp->ID, '_esp_mp_district_id', true);
        $district_name = '';
        if ($district_id) {
            $district = get_post($district_id);
            if ($district && $district->post_status === 'publish') {
                $district_name = $district->post_title;
            }
        }
        $message = $mp->post_content; // Message is now from post content

        ob_start();
        ?>
        <section class="esp-parliament-section esp-single-mp">
            <div class="esp-mp-card esp-single-mp-card">
                <?php if ($atts['show_photo'] === 'true'): ?>
                    <div class="esp-mp-photo">
                        <?php if (has_post_thumbnail($mp->ID)): ?>
                            <?php echo get_the_post_thumbnail($mp->ID, 'medium'); ?>
                        <?php else: ?>
                            👤
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <div class="esp-mp-details">
                    <div class="esp-mp-name"><?php echo esc_html($mp->post_title); ?></div>

                    <?php if ($electorate): ?>
                        <div class="esp-mp-electorate"><?php echo esc_html($electorate); ?></div>
                    <?php endif; ?>

                    <?php if ($party): ?>
                        <div class="esp-mp-party"><?php echo esc_html($party); ?></div>
                <?php endif; ?>

                <?php if ($district_name): ?>
                    <div class="esp-mp-district"><?php echo esc_html($district_name); ?></div>
                    <?php endif; ?>

                    <?php if ($atts['show_message'] === 'true' && $message): ?>
                        <div class="esp-mp-message"><?php echo wp_kses_post(wpautop($message)); ?></div>
                    <?php endif; ?>
                </div>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }

    /**
     * MP Message shortcode - displays only the MP's message (post content)
     */
    public function mp_message_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => '',
            'level' => 'district',
            'district_id' => ''
        ), $atts);

        // Check if MP ID is provided
        if (empty($atts['id'])) {
            return '<p>' . __('MP ID is required. Usage: [dakoii_mp_message id="123"]', 'esp-admin-manager') . '</p>';
        }

        // Check permissions
        $district_id = !empty($atts['district_id']) ? intval($atts['district_id']) : null;
        if (!$this->check_shortcode_permission('mps', $atts['level'], $district_id)) {
            return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
        }

        // Get the specific MP
        $mp = get_post(intval($atts['id']));

        if (!$mp || $mp->post_type !== 'esp_mp' || $mp->post_status !== 'publish') {
            return '<p>' . __('MP not found or not published.', 'esp-admin-manager') . '</p>';
        }

        // Filter content by user access
        $mps_array = array($mp);
        $filtered_mps = $this->filter_content_by_district_access($mps_array);

        if (empty($filtered_mps)) {
            return '<p>' . __('You do not have permission to view this MP.', 'esp-admin-manager') . '</p>';
        }

        // Get the MP message (post content)
        $message = $mp->post_content;

        if (empty($message)) {
            return '<p>' . __('No message available for this MP.', 'esp-admin-manager') . '</p>';
        }

        ob_start();
        ?>
        <div class="esp-mp-message-only">
            <?php echo wp_kses_post(wpautop($message)); ?>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Districts shortcode
     */
    public function districts_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => -1,
            'show_stats' => 'true',
            'level' => 'district',
            'district_id' => ''
        ), $atts);

        // Check permissions
        $district_id = !empty($atts['district_id']) ? intval($atts['district_id']) : null;
        if (!$this->check_shortcode_permission('districts', $atts['level'], $district_id)) {
            return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
        }

        $districts = get_posts(array(
            'post_type' => 'esp_district',
            'numberposts' => intval($atts['limit']),
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));

        // Filter content by user access
        $districts = $this->filter_content_by_district_access($districts);

        if (empty($districts)) {
            return '<p>' . __('No districts found.', 'esp-admin-manager') . '</p>';
        }
        
        ob_start();
        ?>
        <section class="esp-districts-section">
            <div class="esp-districts-grid">
                <?php foreach ($districts as $district):
                    $llgs = get_post_meta($district->ID, '_esp_district_llgs', true);
                    $wards = get_post_meta($district->ID, '_esp_district_wards', true);
                    $population = get_post_meta($district->ID, '_esp_district_population', true);
                    $area = get_post_meta($district->ID, '_esp_district_area', true);
                    $page_url = get_post_meta($district->ID, '_esp_district_page_url', true);
                    $photo_url = get_the_post_thumbnail_url($district->ID, 'medium');

                    // Determine if the card should be clickable
                    $is_clickable = !empty($page_url);
                    $card_tag = $is_clickable ? 'a' : 'div';
                    $card_attributes = $is_clickable ? 'href="' . esc_url($page_url) . '"' : '';
                    $card_class = 'esp-district-card' . ($is_clickable ? ' esp-district-card-clickable' : '');
                ?>
                    <<?php echo $card_tag; ?> class="<?php echo esc_attr($card_class); ?>" <?php echo $card_attributes; ?>>
                        <div class="esp-district-photo">
                            <?php if ($photo_url): ?>
                                <img src="<?php echo esc_url($photo_url); ?>" alt="<?php echo esc_attr($district->post_title); ?>">
                            <?php else: ?>
                                <div class="esp-district-placeholder">
                                    <span class="esp-placeholder-icon">🏛️</span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="esp-district-info">
                            <h3 class="esp-district-name"><?php echo esc_html($district->post_title); ?></h3>

                            <?php if ($atts['show_stats'] === 'true' && ($llgs || $wards)): ?>
                                <div class="esp-district-stats">
                                    <?php if ($llgs): ?>
                                        <div class="esp-district-stat">
                                            <span class="esp-stat-number"><?php echo esc_html($llgs); ?></span>
                                            <span class="esp-stat-label"><?php _e('LLGs', 'esp-admin-manager'); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($wards): ?>
                                        <div class="esp-district-stat">
                                            <span class="esp-stat-number"><?php echo esc_html($wards); ?></span>
                                            <span class="esp-stat-label"><?php _e('Wards', 'esp-admin-manager'); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($population): ?>
                                        <div class="esp-district-stat">
                                            <span class="esp-stat-number"><?php echo esc_html(number_format($population)); ?></span>
                                            <span class="esp-stat-label"><?php _e('Population', 'esp-admin-manager'); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($area): ?>
                                        <div class="esp-district-stat">
                                            <span class="esp-stat-number"><?php echo esc_html($area); ?></span>
                                            <span class="esp-stat-label"><?php _e('km²', 'esp-admin-manager'); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($district->post_content): ?>
                                <div class="esp-district-description">
                                    <?php echo wp_kses_post(wp_trim_words($district->post_content, 20, '...')); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </<?php echo $card_tag; ?>>
                <?php endforeach; ?>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Statistics shortcode
     */
    public function statistics_shortcode($atts) {
        $atts = shortcode_atts(array(
            'layout' => 'grid', // grid or list
            'level' => 'provincial',
            'district_id' => ''
        ), $atts);

        // Check permissions
        $district_id = !empty($atts['district_id']) ? intval($atts['district_id']) : null;
        if (!$this->check_shortcode_permission('statistics', $atts['level'], $district_id)) {
            return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
        }

        $statistics = get_option('esp_provincial_statistics', array());

        if (empty($statistics)) {
            return '<p>' . __('No provincial statistics found.', 'esp-admin-manager') . '</p>';
        }
        
        ob_start();
        ?>
        <section class="esp-statistics-section">
            <div class="esp-map-stats <?php echo esc_attr($atts['layout']); ?>">
                <?php if (isset($statistics['population'])): ?>
                    <div class="esp-map-stat">
                        <div class="esp-map-stat-number"><?php echo esc_html($statistics['population']); ?></div>
                        <div class="esp-map-stat-label"><?php _e('Population', 'esp-admin-manager'); ?></div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($statistics['area'])): ?>
                    <div class="esp-map-stat">
                        <div class="esp-map-stat-number"><?php echo esc_html($statistics['area']); ?></div>
                        <div class="esp-map-stat-label"><?php _e('Area (km²)', 'esp-admin-manager'); ?></div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($statistics['districts'])): ?>
                    <div class="esp-map-stat">
                        <div class="esp-map-stat-number"><?php echo esc_html($statistics['districts']); ?></div>
                        <div class="esp-map-stat-label"><?php _e('Districts', 'esp-admin-manager'); ?></div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($statistics['llgs'])): ?>
                    <div class="esp-map-stat">
                        <div class="esp-map-stat-number"><?php echo esc_html($statistics['llgs']); ?></div>
                        <div class="esp-map-stat-label"><?php _e('LLGs', 'esp-admin-manager'); ?></div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($statistics['wards'])): ?>
                    <div class="esp-map-stat">
                        <div class="esp-map-stat-number"><?php echo esc_html($statistics['wards']); ?></div>
                        <div class="esp-map-stat-label"><?php _e('Wards', 'esp-admin-manager'); ?></div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($statistics['urban_llgs'])): ?>
                    <div class="esp-map-stat">
                        <div class="esp-map-stat-number"><?php echo esc_html($statistics['urban_llgs']); ?></div>
                        <div class="esp-map-stat-label"><?php _e('Urban LLGs', 'esp-admin-manager'); ?></div>
                    </div>
                <?php endif; ?>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Events shortcode
     */
    public function events_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 5,
            'upcoming_only' => 'true',
            'show_images' => 'true',
            'level' => 'district',
            'district_id' => ''
        ), $atts);

        // Check permissions
        $district_id = !empty($atts['district_id']) ? intval($atts['district_id']) : null;
        if (!$this->check_shortcode_permission('events', $atts['level'], $district_id)) {
            return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
        }

        $args = array(
            'post_type' => 'esp_event',
            'numberposts' => intval($atts['limit']),
            'post_status' => 'publish',
            'orderby' => 'meta_value',
            'meta_key' => '_esp_event_start_date',
            'order' => 'ASC'
        );

        if ($atts['upcoming_only'] === 'true') {
            $args['meta_query'] = array(
                array(
                    'key' => '_esp_event_start_date',
                    'value' => date('Y-m-d'),
                    'compare' => '>='
                )
            );
        }

        $events = get_posts($args);

        // Filter content by user access
        $events = $this->filter_content_by_district_access($events);

        if (empty($events)) {
            return '<p>' . __('No events found.', 'esp-admin-manager') . '</p>';
        }

        ob_start();
        ?>
        <section class="esp-events-section">
            <div class="esp-events-grid">
                <?php foreach ($events as $event):
                    $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
                    $end_date = get_post_meta($event->ID, '_esp_event_end_date', true);
                    $location = get_post_meta($event->ID, '_esp_event_location', true);
                ?>
                    <div class="esp-event-card">
                        <a href="<?php echo get_permalink($event->ID); ?>" class="esp-event-link">
                            <?php if ($atts['show_images'] === 'true'): ?>
                                <div class="esp-event-image">
                                    <?php if (has_post_thumbnail($event->ID)): ?>
                                        <?php echo get_the_post_thumbnail($event->ID, 'medium'); ?>
                                    <?php else: ?>
                                        <div class="esp-event-placeholder">
                                            📅
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <div class="esp-event-content">
                                <div class="esp-event-date">
                                    <?php
                                    if ($start_date) {
                                        $formatted_start = date('F j, Y', strtotime($start_date));
                                        if ($end_date && $end_date !== $start_date) {
                                            $formatted_end = date('F j, Y', strtotime($end_date));
                                            echo esc_html($formatted_start . ' - ' . $formatted_end);
                                        } else {
                                            echo esc_html($formatted_start);
                                        }
                                    }
                                    ?>
                                </div>
                                <div class="esp-event-title"><?php echo esc_html($event->post_title); ?></div>
                                <?php if ($location): ?>
                                    <div class="esp-event-location">📍 <?php echo esc_html($location); ?></div>
                                <?php endif; ?>
                                <?php if ($event->post_content): ?>
                                    <div class="esp-event-description"><?php echo wp_kses_post(wp_trim_words($event->post_content, 25)); ?></div>
                                <?php endif; ?>
                                <div class="esp-event-read-more">
                                    <?php _e('View Full Details →', 'esp-admin-manager'); ?>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }
    
    /**
     * News shortcode
     */
    public function news_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 5,
            'featured_only' => 'false',
            'show_images' => 'true',
            'level' => 'district',
            'district_id' => ''
        ), $atts);

        // Check permissions
        $district_id = !empty($atts['district_id']) ? intval($atts['district_id']) : null;
        if (!$this->check_shortcode_permission('news', $atts['level'], $district_id)) {
            return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
        }

        $args = array(
            'post_type' => 'esp_news',
            'numberposts' => intval($atts['limit']),
            'post_status' => 'publish',
            'orderby' => 'date',
            'order' => 'DESC'
        );

        if ($atts['featured_only'] === 'true') {
            $args['meta_query'] = array(
                array(
                    'key' => '_esp_news_featured',
                    'value' => '1',
                    'compare' => '='
                )
            );
        }

        $news = get_posts($args);

        // Filter content by user access
        $news = $this->filter_content_by_district_access($news);

        if (empty($news)) {
            return '<p>' . __('No news found.', 'esp-admin-manager') . '</p>';
        }

        ob_start();
        ?>
        <section class="esp-news-section">
            <div class="esp-news-grid">
                <?php foreach ($news as $news_item):
                    $news_date = get_post_meta($news_item->ID, '_esp_news_date', true);
                    $source = get_post_meta($news_item->ID, '_esp_news_source', true);
                ?>
                    <div class="esp-news-card">
                        <a href="<?php echo get_permalink($news_item->ID); ?>" class="esp-news-link">
                            <?php if ($atts['show_images'] === 'true'): ?>
                                <div class="esp-news-image">
                                    <?php if (has_post_thumbnail($news_item->ID)): ?>
                                        <?php echo get_the_post_thumbnail($news_item->ID, 'medium'); ?>
                                    <?php else: ?>
                                        <div class="esp-news-placeholder">
                                            📰
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <div class="esp-news-content">
                                <div class="esp-news-date">
                                    <?php
                                    if ($news_date) {
                                        echo esc_html(date('F j, Y', strtotime($news_date)));
                                    } else {
                                        echo esc_html(get_the_date('F j, Y', $news_item));
                                    }
                                    ?>
                                </div>
                                <div class="esp-news-title"><?php echo esc_html($news_item->post_title); ?></div>
                                <?php if ($source): ?>
                                    <div class="esp-news-source">📢 <?php echo esc_html($source); ?></div>
                                <?php endif; ?>
                                <div class="esp-news-excerpt">
                                    <?php
                                    if ($news_item->post_excerpt) {
                                        echo wp_kses_post($news_item->post_excerpt);
                                    } else {
                                        echo wp_kses_post(wp_trim_words($news_item->post_content, 25));
                                    }
                                    ?>
                                </div>
                                <div class="esp-news-read-more">
                                    <?php _e('Read Full Article →', 'esp-admin-manager'); ?>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }

    /**
     * Recent News shortcode - optimized for displaying recent news
     */
    public function recent_news_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 3,
            'show_images' => 'true',
            'level' => 'district',
            'district_id' => ''
        ), $atts);

        // Force recent news settings
        $news_atts = array(
            'limit' => $atts['limit'],
            'featured_only' => 'false',
            'show_images' => $atts['show_images'],
            'level' => $atts['level'],
            'district_id' => $atts['district_id']
        );

        // Get the news content using existing news shortcode
        $content = $this->news_shortcode($news_atts);

        // Wrap in recent news specific container for custom styling
        if (strpos($content, 'esp-news-section') !== false) {
            $content = str_replace('esp-news-section', 'esp-news-section esp-recent-news', $content);
        }

        return $content;
    }

    /**
     * Upcoming Events shortcode - optimized for displaying upcoming events
     */
    public function upcoming_events_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 3,
            'show_images' => 'true',
            'level' => 'district',
            'district_id' => ''
        ), $atts);

        // Force upcoming events settings
        $events_atts = array(
            'limit' => $atts['limit'],
            'upcoming_only' => 'true',
            'show_images' => $atts['show_images'],
            'level' => $atts['level'],
            'district_id' => $atts['district_id']
        );

        // Get the events content using existing events shortcode
        $content = $this->events_shortcode($events_atts);

        // Wrap in upcoming events specific container for custom styling
        if (strpos($content, 'esp-events-section') !== false) {
            $content = str_replace('esp-events-section', 'esp-events-section esp-upcoming-events', $content);
        }

        return $content;
    }

    /**
     * Contact shortcode
     */
    public function contact_shortcode($atts) {
        $atts = shortcode_atts(array(
            'layout' => 'grid', // grid or list
            'level' => 'provincial',
            'district_id' => '',
            'id' => '' // For multiple provincial contacts
        ), $atts);

        // Check permissions
        $district_id = !empty($atts['district_id']) ? intval($atts['district_id']) : null;
        if (!$this->check_shortcode_permission('contact', $atts['level'], $district_id)) {
            return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
        }

        // Determine contact source and get data
        if ($district_id) {
            // District contact
            $contact = $this->get_district_contact_data($district_id);
            $contact_type = 'district';
            $contact_name = get_the_title($district_id) . ' District';
        } elseif (!empty($atts['id'])) {
            // Contact post
            $contact = $this->get_contact_post_data($atts['id']);
            $contact_type = 'contact_post';
            $contact_name = isset($contact['name']) ? $contact['name'] : 'Contact';
        } else {
            // No contact specified
            return '<p>' . __('Contact ID is required. Use: [dakoii_contact id="123"] or [dakoii_contact district_id="456"]', 'esp-admin-manager') . '</p>';
        }

        if (empty($contact) || (isset($contact['name']) && empty(array_filter($contact, function($v, $k) { return $k !== 'name' && $k !== 'id' && !empty($v); }, ARRAY_FILTER_USE_BOTH)))) {
            if ($district_id) {
                return '<p>' . sprintf(__('No contact information found for %s District.', 'esp-admin-manager'), esc_html(get_the_title($district_id))) . '</p>';
            } elseif (!empty($atts['id'])) {
                return '<p>' . sprintf(__('No contact information found for contact ID: %s', 'esp-admin-manager'), esc_html($atts['id'])) . '</p>';
            } else {
                return '<p>' . __('No contact information found.', 'esp-admin-manager') . '</p>';
            }
        }
        
        ob_start();
        ?>
        <section class="esp-contact-section">
            <?php if ($contact_type === 'district' || $contact_type === 'contact_post'): ?>
                <div class="esp-contact-header" style="margin-bottom: 20px; text-align: center;">
                    <h2 style="color: var(--esp-primary); margin-bottom: 5px;"><?php echo esc_html($contact_name); ?></h2>
                    <p style="color: #666; margin: 0;"><?php _e('Contact Information', 'esp-admin-manager'); ?></p>
                </div>
            <?php endif; ?>
            <div class="esp-contact-grid <?php echo esc_attr($atts['layout']); ?>">
                <?php if (isset($contact['address']) && !empty($contact['address'])): ?>
                    <div class="esp-contact-card">
                        <div class="esp-contact-icon">🏛️</div>
                        <h3 class="esp-contact-title">
                            <?php if ($contact_type === 'district'): ?>
                                <?php _e('District Office', 'esp-admin-manager'); ?>
                            <?php elseif ($contact_type === 'contact_post'): ?>
                                <?php _e('Office Address', 'esp-admin-manager'); ?>
                            <?php else: ?>
                                <?php _e('Address', 'esp-admin-manager'); ?>
                            <?php endif; ?>
                        </h3>
                        <div class="esp-contact-info"><?php echo nl2br(esc_html($contact['address'])); ?></div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($contact['phone']) || isset($contact['fax']) || isset($contact['emergency'])): ?>
                    <div class="esp-contact-card">
                        <div class="esp-contact-icon">📞</div>
                        <h3 class="esp-contact-title"><?php _e('Phone & Fax', 'esp-admin-manager'); ?></h3>
                        <div class="esp-contact-info">
                            <?php if (isset($contact['phone'])): ?>
                                <?php _e('Phone:', 'esp-admin-manager'); ?> <?php echo esc_html($contact['phone']); ?><br>
                            <?php endif; ?>
                            <?php if (isset($contact['fax'])): ?>
                                <?php _e('Fax:', 'esp-admin-manager'); ?> <?php echo esc_html($contact['fax']); ?><br>
                            <?php endif; ?>
                            <?php if (isset($contact['emergency'])): ?>
                                <?php _e('Emergency:', 'esp-admin-manager'); ?> <?php echo esc_html($contact['emergency']); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($contact['email']) || isset($contact['admin_email']) || isset($contact['website'])): ?>
                    <div class="esp-contact-card">
                        <div class="esp-contact-icon">✉️</div>
                        <h3 class="esp-contact-title"><?php _e('Email & Web', 'esp-admin-manager'); ?></h3>
                        <div class="esp-contact-info">
                            <?php if (isset($contact['email'])): ?>
                                <a href="mailto:<?php echo esc_attr($contact['email']); ?>"><?php echo esc_html($contact['email']); ?></a><br>
                            <?php endif; ?>
                            <?php if (isset($contact['admin_email'])): ?>
                                <a href="mailto:<?php echo esc_attr($contact['admin_email']); ?>"><?php echo esc_html($contact['admin_email']); ?></a><br>
                            <?php endif; ?>
                            <?php if (isset($contact['website'])): ?>
                                <a href="<?php echo esc_url('http://' . $contact['website']); ?>" target="_blank"><?php echo esc_html($contact['website']); ?></a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($contact['office_hours'])): ?>
                    <div class="esp-contact-card">
                        <div class="esp-contact-icon">🕐</div>
                        <h3 class="esp-contact-title"><?php _e('Office Hours', 'esp-admin-manager'); ?></h3>
                        <div class="esp-contact-info"><?php echo nl2br(esc_html($contact['office_hours'])); ?></div>
                    </div>
                <?php endif; ?>
            </div>
        </section>
        <?php
        return ob_get_clean();
    }

    /**
     * Get district contact data
     */
    private function get_district_contact_data($district_id) {
        // Verify district exists
        if (!get_post($district_id) || get_post_type($district_id) !== 'esp_district') {
            return array();
        }

        return array(
            'address' => get_post_meta($district_id, '_esp_district_contact_address', true),
            'phone' => get_post_meta($district_id, '_esp_district_contact_phone', true),
            'fax' => get_post_meta($district_id, '_esp_district_contact_fax', true),
            'emergency' => get_post_meta($district_id, '_esp_district_contact_emergency', true),
            'email' => get_post_meta($district_id, '_esp_district_contact_email', true),
            'admin_email' => get_post_meta($district_id, '_esp_district_contact_admin_email', true),
            'website' => get_post_meta($district_id, '_esp_district_contact_website', true),
            'office_hours' => get_post_meta($district_id, '_esp_district_contact_office_hours', true)
        );
    }

    /**
     * Get contact post data
     */
    private function get_contact_post_data($contact_id) {
        // Verify contact post exists
        $contact_post = get_post($contact_id);
        if (!$contact_post || get_post_type($contact_id) !== 'esp_contact') {
            return array();
        }

        return array(
            'name' => $contact_post->post_title,
            'description' => $contact_post->post_content,
            'address' => get_post_meta($contact_id, '_esp_contact_address', true),
            'phone' => get_post_meta($contact_id, '_esp_contact_phone', true),
            'fax' => get_post_meta($contact_id, '_esp_contact_fax', true),
            'emergency' => get_post_meta($contact_id, '_esp_contact_emergency', true),
            'email' => get_post_meta($contact_id, '_esp_contact_email', true),
            'admin_email' => get_post_meta($contact_id, '_esp_contact_admin_email', true),
            'website' => get_post_meta($contact_id, '_esp_contact_website', true),
            'office_hours' => get_post_meta($contact_id, '_esp_contact_office_hours', true)
        );
    }

    /**
     * District contact shortcode (alternative syntax)
     */
    public function district_contact_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => '',
            'layout' => 'grid'
        ), $atts);

        if (empty($atts['id'])) {
            return '<p>' . __('District ID is required for this shortcode.', 'esp-admin-manager') . '</p>';
        }

        // Use the main contact shortcode with district_id parameter
        return $this->contact_shortcode(array(
            'district_id' => $atts['id'],
            'layout' => $atts['layout'],
            'level' => 'district'
        ));
    }

    /**
     * Provincial contact shortcode (alternative syntax for multiple contacts)
     */
    public function provincial_contact_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => '',
            'layout' => 'grid'
        ), $atts);

        if (empty($atts['id'])) {
            return '<p>' . __('Contact ID is required for this shortcode.', 'esp-admin-manager') . '</p>';
        }

        // Use the main contact shortcode with id parameter
        return $this->contact_shortcode(array(
            'id' => $atts['id'],
            'layout' => $atts['layout'],
            'level' => 'provincial'
        ));
    }

    /**
     * Slideshow shortcode
     */
    public function slideshow_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => 0,
            'tag' => '',
            'autoplay' => 'true',
            'duration' => '5000',
            'show_nav' => 'true',
            'show_dots' => 'true',
            'height' => '400',
            'level' => 'provincial'
        ), $atts);

        // Check permissions
        if (!$this->check_shortcode_permission('slideshow', $atts['level'])) {
            return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
        }

        // Get slideshow data
        if (!empty($atts['id'])) {
            return $this->render_slideshow_by_id($atts['id'], $atts);
        } elseif (!empty($atts['tag'])) {
            return $this->render_slideshow_by_tag($atts['tag'], $atts);
        } else {
            // Fallback to default slideshow for backward compatibility
            return $this->render_default_slideshow($atts);
        }
    }

    /**
     * Render slideshow by ID
     */
    private function render_slideshow_by_id($group_id, $options) {
        $slides = Provincial_Slideshow::get_slides($group_id);
        if (empty($slides)) {
            return '<p class="esp-slideshow-error">' . __('No slides found for this slideshow.', 'esp-admin-manager') . '</p>';
        }

        return $this->render_slideshow($slides, $group_id, $options);
    }

    /**
     * Render slideshow by tag
     */
    private function render_slideshow_by_tag($tag, $options) {
        $groups = Provincial_Slideshow::get_groups_by_tag($tag);
        if (empty($groups)) {
            return '<p class="esp-slideshow-error">' . __('No slideshow found with tag: ', 'esp-admin-manager') . esc_html($tag) . '</p>';
        }

        // Use the first group found with this tag
        $group = $groups[0];
        $slides = Provincial_Slideshow::get_slides($group->id);

        if (empty($slides)) {
            return '<p class="esp-slideshow-error">' . __('No slides found for this slideshow.', 'esp-admin-manager') . '</p>';
        }

        return $this->render_slideshow($slides, $group->id, $options);
    }

    /**
     * Render slideshow HTML
     */
    private function render_slideshow($slides, $group_id, $options) {
        $slideshow_id = 'esp-slideshow-' . $group_id . '-' . wp_rand(1000, 9999);
        $autoplay = ($options['autoplay'] === 'true' || $options['autoplay'] === '1');
        $show_nav = ($options['show_nav'] === 'true' || $options['show_nav'] === '1');
        $show_dots = ($options['show_dots'] === 'true' || $options['show_dots'] === '1');
        $duration = intval($options['duration']);
        $height = intval($options['height']);

        ob_start();
        ?>
        <div class="esp-slideshow" id="<?php echo esc_attr($slideshow_id); ?>"
             data-autoplay="<?php echo $autoplay ? 'true' : 'false'; ?>"
             data-duration="<?php echo $duration; ?>"
             style="height: <?php echo $height; ?>px;">

            <div class="esp-slideshow-container">
                <?php foreach ($slides as $index => $slide): ?>
                    <div class="esp-slide <?php echo $index === 0 ? 'active' : ''; ?>" data-slide="<?php echo $index; ?>">
                        <?php if (!empty($slide->link_url)): ?>
                            <a href="<?php echo esc_url($slide->link_url); ?>" class="esp-slide-link">
                        <?php endif; ?>

                        <img src="<?php echo esc_url($slide->image_url); ?>"
                             alt="<?php echo esc_attr($slide->title); ?>"
                             class="esp-slide-image">

                        <?php if (!empty($slide->title) || !empty($slide->description)): ?>
                            <div class="esp-slide-content">
                                <?php if (!empty($slide->title)): ?>
                                    <h3 class="esp-slide-title"><?php echo esc_html($slide->title); ?></h3>
                                <?php endif; ?>
                                <?php if (!empty($slide->description)): ?>
                                    <p class="esp-slide-description"><?php echo esc_html($slide->description); ?></p>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($slide->link_url)): ?>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <?php if ($show_nav && count($slides) > 1): ?>
                <button class="esp-slideshow-nav esp-prev" onclick="espSlideshowPrev('<?php echo esc_js($slideshow_id); ?>')" aria-label="<?php _e('Previous slide', 'esp-admin-manager'); ?>">
                    <span class="esp-nav-icon">‹</span>
                </button>
                <button class="esp-slideshow-nav esp-next" onclick="espSlideshowNext('<?php echo esc_js($slideshow_id); ?>')" aria-label="<?php _e('Next slide', 'esp-admin-manager'); ?>">
                    <span class="esp-nav-icon">›</span>
                </button>
            <?php endif; ?>

            <?php if ($show_dots && count($slides) > 1): ?>
                <div class="esp-slideshow-dots">
                    <?php foreach ($slides as $index => $slide): ?>
                        <button class="esp-dot <?php echo $index === 0 ? 'active' : ''; ?>"
                                onclick="espSlideshowGoTo('<?php echo esc_js($slideshow_id); ?>', <?php echo $index; ?>)"
                                aria-label="<?php printf(__('Go to slide %d', 'esp-admin-manager'), $index + 1); ?>"></button>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        <?php

        return ob_get_clean();
    }

    /**
     * Render default slideshow for backward compatibility
     */
    private function render_default_slideshow($options) {
        $slideshow_id = 'esp-slideshow-default-' . wp_rand(1000, 9999);
        $autoplay = ($options['autoplay'] === 'true' || $options['autoplay'] === '1');
        $duration = intval($options['duration']);
        $height = intval($options['height']);

        ob_start();
        ?>
        <div class="esp-slideshow esp-slideshow-default" id="<?php echo esc_attr($slideshow_id); ?>"
             data-autoplay="<?php echo $autoplay ? 'true' : 'false'; ?>"
             data-duration="<?php echo $duration; ?>"
             style="height: <?php echo $height; ?>px;">

            <div class="esp-slideshow-container">
                <div class="esp-slide active" data-slide="0">
                    <div class="esp-slide-content esp-slide-content-center">
                        <h1 class="esp-slide-title"><?php _e('Welcome to East Sepik Province', 'esp-admin-manager'); ?></h1>
                        <p class="esp-slide-description"><?php _e('The Heart of Papua New Guinea\'s Cultural Heritage', 'esp-admin-manager'); ?></p>
                    </div>
                </div>
                <div class="esp-slide" data-slide="1">
                    <div class="esp-slide-content esp-slide-content-center">
                        <h1 class="esp-slide-title"><?php _e('Serving Our Communities', 'esp-admin-manager'); ?></h1>
                        <p class="esp-slide-description"><?php _e('Committed to Development and Progress', 'esp-admin-manager'); ?></p>
                    </div>
                </div>
                <div class="esp-slide" data-slide="2">
                    <div class="esp-slide-content esp-slide-content-center">
                        <h1 class="esp-slide-title"><?php _e('Building a Better Future', 'esp-admin-manager'); ?></h1>
                        <p class="esp-slide-description"><?php _e('Together We Grow Stronger', 'esp-admin-manager'); ?></p>
                    </div>
                </div>
            </div>

            <button class="esp-slideshow-nav esp-prev" onclick="espSlideshowPrev('<?php echo esc_js($slideshow_id); ?>')" aria-label="<?php _e('Previous slide', 'esp-admin-manager'); ?>">
                <span class="esp-nav-icon">‹</span>
            </button>
            <button class="esp-slideshow-nav esp-next" onclick="espSlideshowNext('<?php echo esc_js($slideshow_id); ?>')" aria-label="<?php _e('Next slide', 'esp-admin-manager'); ?>">
                <span class="esp-nav-icon">›</span>
            </button>

            <div class="esp-slideshow-dots">
                <button class="esp-dot active" onclick="espSlideshowGoTo('<?php echo esc_js($slideshow_id); ?>', 0)" aria-label="<?php _e('Go to slide 1', 'esp-admin-manager'); ?>"></button>
                <button class="esp-dot" onclick="espSlideshowGoTo('<?php echo esc_js($slideshow_id); ?>', 1)" aria-label="<?php _e('Go to slide 2', 'esp-admin-manager'); ?>"></button>
                <button class="esp-dot" onclick="espSlideshowGoTo('<?php echo esc_js($slideshow_id); ?>', 2)" aria-label="<?php _e('Go to slide 3', 'esp-admin-manager'); ?>"></button>
            </div>
        </div>
        <?php

        return ob_get_clean();
    }

    /**
     * Single map shortcode
     */
    public function map_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => '',
            'width' => '100%',
            'height' => '400px'
        ), $atts);

        if (empty($atts['id'])) {
            return '<p>' . __('Map ID required', 'esp-admin-manager') . '</p>';
        }

        $map = get_post($atts['id']);
        if (!$map || $map->post_type !== 'esp_map') {
            return '<p>' . __('Map not found', 'esp-admin-manager') . '</p>';
        }

        $json_file = get_post_meta($map->ID, '_esp_map_json_file', true);
        if (empty($json_file)) {
            return '<p>' . __('Map data not available', 'esp-admin-manager') . '</p>';
        }

        $upload_dir = wp_upload_dir();
        $json_url = $upload_dir['baseurl'] . '/provincial-maps/' . $json_file;

        $map_container_id = 'esp-map-' . $map->ID . '-' . uniqid();

        ob_start();
        ?>
        <div class="esp-map-container" style="width: <?php echo esc_attr($atts['width']); ?>; height: <?php echo esc_attr($atts['height']); ?>;">
            <div class="esp-map-header">
                <h3><?php echo esc_html($map->post_title); ?></h3>
                <?php if (!empty($map->post_content)): ?>
                    <p><?php echo esc_html($map->post_content); ?></p>
                <?php endif; ?>
            </div>
            <div id="<?php echo esc_attr($map_container_id); ?>"
                 class="esp-map-display"
                 data-json-url="<?php echo esc_url($json_url); ?>"
                 data-map-title="<?php echo esc_attr($map->post_title); ?>"
                 style="width: 100%; height: <?php echo esc_attr($atts['height']); ?>; min-height: 400px;">
                <div class="esp-map-loading">
                    <p><?php _e('Loading map...', 'esp-admin-manager'); ?></p>
                    <div class="esp-loading-spinner"></div>
                    <p style="font-size: 12px; margin-top: 10px;">
                        <strong>Debug Info:</strong><br>
                        Map ID: <?php echo esc_html($map->ID); ?><br>
                        JSON File: <?php echo esc_html($json_file); ?><br>
                        JSON URL: <a href="<?php echo esc_url($json_url); ?>" target="_blank" style="color: #0073aa;">Test JSON</a><br>
                        Container ID: <?php echo esc_html($map_container_id); ?>
                    </p>
                </div>
            </div>
        </div>

        <script>


        // Direct map initialization function
        function initSingleMap() {
            const containerId = '<?php echo esc_js($map_container_id); ?>';
            const jsonUrl = '<?php echo esc_js($json_url); ?>';
            const mapTitle = '<?php echo esc_js($map->post_title); ?>';

            if (typeof L === 'undefined') {
                document.getElementById(containerId).innerHTML = '<div class="esp-map-error"><h4>Leaflet Library Not Loaded</h4><p>Please refresh the page.</p></div>';
                return;
            }

            try {
                // Check if map container is already initialized
                const container = document.getElementById(containerId);
                if (container._leaflet_id) {
                    return;
                }
                const map = L.map(containerId, {
                    zoomControl: true,
                    scrollWheelZoom: true,
                    doubleClickZoom: true,
                    boxZoom: true,
                    keyboard: true,
                    dragging: true,
                    touchZoom: true
                });

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© OpenStreetMap contributors',
                    maxZoom: 18,
                    minZoom: 2
                }).addTo(map);

                fetch(jsonUrl)
                    .then(response => response.json())
                    .then(geoJsonData => {
                        const geoJsonLayer = L.geoJSON(geoJsonData, {
                            style: function(feature) {
                                return {
                                    fillColor: '#006A4E',
                                    weight: 2,
                                    opacity: 1,
                                    color: '#CE1126',
                                    dashArray: '3',
                                    fillOpacity: 0.3
                                };
                            },
                            onEachFeature: function(feature, layer) {
                                if (feature.properties) {
                                    let popupContent = '<div class="esp-map-popup">';
                                    if (feature.properties.PROVNAME) {
                                        popupContent += '<h4>' + feature.properties.PROVNAME + '</h4>';
                                    }
                                    if (feature.properties.PROVID) {
                                        popupContent += '<p><strong>Province ID:</strong> ' + feature.properties.PROVID + '</p>';
                                    }
                                    popupContent += '</div>';
                                    layer.bindPopup(popupContent);
                                }

                                layer.on({
                                    mouseover: function(e) {
                                        const layer = e.target;
                                        layer.setStyle({
                                            weight: 3,
                                            color: '#FFD700',
                                            dashArray: '',
                                            fillOpacity: 0.5
                                        });
                                        layer.bringToFront();
                                    },
                                    mouseout: function(e) {
                                        geoJsonLayer.resetStyle(e.target);
                                    }
                                });
                            }
                        }).addTo(map);

                        if (geoJsonLayer.getBounds().isValid()) {
                            map.fitBounds(geoJsonLayer.getBounds(), { padding: [20, 20] });
                        }

                        // Remove loading indicator
                        const loadingEl = document.querySelector('#' + containerId + ' .esp-map-loading');
                        if (loadingEl) {
                            loadingEl.style.display = 'none';
                        }

                    })
                    .catch(error => {
                        document.getElementById(containerId).innerHTML = '<div class="esp-map-error"><h4>Error Loading Map Data</h4><p>' + error.message + '</p></div>';
                    });

            } catch (error) {
                document.getElementById(containerId).innerHTML = '<div class="esp-map-error"><h4>Map Creation Error</h4><p>' + error.message + '</p></div>';
            }
        }

        // Try to initialize immediately if Leaflet is available
        if (typeof L !== 'undefined') {
            setTimeout(initSingleMap, 100);
        } else {
            // Wait for Leaflet to load
            let attempts = 0;
            const checkLeaflet = setInterval(function() {
                attempts++;
                if (typeof L !== 'undefined') {
                    clearInterval(checkLeaflet);
                    initSingleMap();
                } else if (attempts > 50) { // 5 seconds
                    clearInterval(checkLeaflet);
                    document.getElementById('<?php echo esc_js($map_container_id); ?>').innerHTML = '<div class="esp-map-error"><h4>Map Library Failed to Load</h4><p>Please check your internet connection and refresh the page.</p></div>';
                }
            }, 100);
        }
        </script>
        <?php
        return ob_get_clean();
    }

    /**
     * Maps list shortcode
     */
    public function maps_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => -1
        ), $atts);

        $maps = get_posts(array(
            'post_type' => 'esp_map',
            'numberposts' => intval($atts['limit']),
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));

        if (empty($maps)) {
            return '<p>' . __('No maps available', 'esp-admin-manager') . '</p>';
        }

        ob_start();
        ?>
        <div class="esp-maps-list">
            <?php foreach ($maps as $map): ?>
            <div class="esp-map-item" style="margin-bottom: 30px; border: 1px solid #ddd; padding: 20px;">
                <h4><?php echo esc_html($map->post_title); ?></h4>
                <?php if (!empty($map->post_content)): ?>
                    <p><?php echo esc_html($map->post_content); ?></p>
                <?php endif; ?>
                <?php echo do_shortcode('[dakoii_map id="' . $map->ID . '"]'); ?>
            </div>
            <?php endforeach; ?>
        </div>
        <?php
        return ob_get_clean();
    }
}
